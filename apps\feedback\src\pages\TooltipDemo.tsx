import React from "react";
import {
  <PERSON>,
  Button,
  OverlayTrigger,
  Tooltip,
  Layout,
  Text
} from "@repo/ui";
import { Info, Plus, Trash2 } from "lucide-react";

const TooltipDemo: React.FC = () => {
  return (
    <Layout.Container className="py-8">
      <div className="max-w-6xl mx-auto">
        <Text.H1 className="mb-6">OverlayTrigger & Tooltip Demo</Text.H1>
        <Text.P1 className="mb-8 text-gray-600">
          React Bootstrap compatible OverlayTrigger and Tooltip components using Tailwind CSS and Radix UI.
          These components maintain full API compatibility with the original react-bootstrap components.
        </Text.P1>

        {/* Basic Tooltip Examples */}
        <Card className="mb-8">
          <Card.Header>
            <Text.H3>Basic Tooltip Placements</Text.H3>
          </Card.Header>
          <Card.Body>
            <Layout.Row className="gap-4">
              <Layout.Col>
                <div className="text-center">
                  <Text.L1 className="block mb-2">Top Placement</Text.L1>
                  <OverlayTrigger
                    placement="top"
                    overlay={
                      <Tooltip id="tooltip-top">
                        This tooltip appears on top!
                      </Tooltip>
                    }
                  >
                    <Button variant="primary">Hover me (Top)</Button>
                  </OverlayTrigger>
                </div>
              </Layout.Col>
              
              <Layout.Col>
                <div className="text-center">
                  <Text.L1 className="block mb-2">Bottom Placement</Text.L1>
                  <OverlayTrigger
                    placement="bottom"
                    overlay={
                      <Tooltip id="tooltip-bottom">
                        This tooltip appears on bottom!
                      </Tooltip>
                    }
                  >
                    <Button variant="secondary">Hover me (Bottom)</Button>
                  </OverlayTrigger>
                </div>
              </Layout.Col>
              
              <Layout.Col>
                <div className="text-center">
                  <Text.L1 className="block mb-2">Left Placement</Text.L1>
                  <OverlayTrigger
                    placement="left"
                    overlay={
                      <Tooltip id="tooltip-left">
                        This tooltip appears on left!
                      </Tooltip>
                    }
                  >
                    <Button variant="success">Hover me (Left)</Button>
                  </OverlayTrigger>
                </div>
              </Layout.Col>
              
              <Layout.Col>
                <div className="text-center">
                  <Text.L1 className="block mb-2">Right Placement</Text.L1>
                  <OverlayTrigger
                    placement="right"
                    overlay={
                      <Tooltip id="tooltip-right">
                        This tooltip appears on right!
                      </Tooltip>
                    }
                  >
                    <Button variant="danger">Hover me (Right)</Button>
                  </OverlayTrigger>
                </div>
              </Layout.Col>
            </Layout.Row>
          </Card.Body>
        </Card>

        {/* Function as Children Pattern */}
        <Card className="mb-8">
          <Card.Header>
            <Text.H3>Function as Children Pattern</Text.H3>
            <Text.P2 className="text-gray-600 mt-2">
              This pattern is commonly used in the old app for custom trigger elements.
            </Text.P2>
          </Card.Header>
          <Card.Body>
            <Layout.Row className="gap-4 items-center">
              <Layout.Col>
                <div className="flex items-center gap-2">
                  <Text.L1>Info Icon with Tooltip:</Text.L1>
                  <OverlayTrigger
                    placement="bottom"
                    overlay={
                      <Tooltip id="tooltip-icon">
                        This tooltip uses the function-as-children pattern like in the old app!
                        Perfect for custom trigger elements.
                      </Tooltip>
                    }
                  >
                    {({ ...triggerHandler }) => (
                      <span {...triggerHandler} className="cursor-pointer">
                        <Info className="w-4 h-4 text-blue-500" />
                      </span>
                    )}
                  </OverlayTrigger>
                </div>
              </Layout.Col>
              
              <Layout.Col>
                <div className="flex items-center gap-2">
                  <Text.L1>Custom Element:</Text.L1>
                  <OverlayTrigger
                    placement="top"
                    overlay={
                      <Tooltip id="tooltip-custom">
                        Custom styled trigger element with tooltip
                      </Tooltip>
                    }
                  >
                    {({ ...triggerHandler }) => (
                      <div 
                        {...triggerHandler}
                        className="w-6 h-6 bg-yellow-400 rounded-full cursor-pointer flex items-center justify-center text-xs font-bold"
                      >
                        ?
                      </div>
                    )}
                  </OverlayTrigger>
                </div>
              </Layout.Col>
            </Layout.Row>
          </Card.Body>
        </Card>

        {/* Long Content and Styling */}
        <Card className="mb-8">
          <Card.Header>
            <Text.H3>Long Content & Custom Styling</Text.H3>
          </Card.Header>
          <Card.Body>
            <Layout.Row className="gap-4">
              <Layout.Col>
                <div className="text-center">
                  <Text.L1 className="block mb-2">Long Text Tooltip</Text.L1>
                  <OverlayTrigger
                    placement="bottom"
                    overlay={
                      <Tooltip id="tooltip-long" className="max-w-xs text-left">
                        This is a very long tooltip text that demonstrates how the tooltip handles longer content. 
                        It should wrap properly and maintain good readability. The tooltip can contain multiple 
                        sentences and will adjust its width accordingly.
                      </Tooltip>
                    }
                  >
                    <Button variant="outline-primary">Long Content</Button>
                  </OverlayTrigger>
                </div>
              </Layout.Col>
              
              <Layout.Col>
                <div className="text-center">
                  <Text.L1 className="block mb-2">Custom Styled</Text.L1>
                  <OverlayTrigger
                    placement="bottom"
                    overlay={
                      <Tooltip id="tooltip-styled" className="bg-purple-600 text-white border-purple-700">
                        Custom styled tooltip with purple background!
                      </Tooltip>
                    }
                  >
                    <Button variant="outline-secondary">Custom Style</Button>
                  </OverlayTrigger>
                </div>
              </Layout.Col>
            </Layout.Row>
          </Card.Body>
        </Card>

        {/* Real-world Examples */}
        <Card className="mb-8">
          <Card.Header>
            <Text.H3>Real-world Usage Examples</Text.H3>
            <Text.P2 className="text-gray-600 mt-2">
              Examples similar to how tooltips are used in the feedback-frontend app.
            </Text.P2>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              {/* Example 1: Form field with help */}
              <div className="flex items-center gap-2">
                <Text.L1>Survey Name</Text.L1>
                <OverlayTrigger
                  placement="right"
                  overlay={
                    <Tooltip id="tooltip-help">
                      Enter a descriptive name for your survey. This will be visible to participants.
                    </Tooltip>
                  }
                >
                  {({ ...triggerHandler }) => (
                    <span {...triggerHandler}>
                      <Info className="w-4 h-4 text-gray-400" />
                    </span>
                  )}
                </OverlayTrigger>
                <input 
                  type="text" 
                  className="ml-4 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter survey name..."
                />
              </div>

              {/* Example 2: Action button with description */}
              <div className="flex items-center gap-4">
                <OverlayTrigger
                  placement="top"
                  overlay={
                    <Tooltip id="tooltip-action">
                      Create a new self-assessment survey for team members
                    </Tooltip>
                  }
                >
                  <Button variant="default" size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Survey
                  </Button>
                </OverlayTrigger>

                <OverlayTrigger
                  placement="top"
                  overlay={
                    <Tooltip id="tooltip-delete">
                      Permanently delete this survey and all associated data
                    </Tooltip>
                  }
                >
                  <Button variant="destructive" size="sm">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </Button>
                </OverlayTrigger>
              </div>
            </div>
          </Card.Body>
        </Card>

        {/* API Compatibility Note */}
        <Card>
          <Card.Header>
            <Text.H3>API Compatibility</Text.H3>
          </Card.Header>
          <Card.Body>
            <Text.P1 className="mb-4">
              These components maintain full compatibility with react-bootstrap OverlayTrigger and Tooltip APIs:
            </Text.P1>
            <ul className="list-disc list-inside space-y-2 text-sm text-gray-700">
              <li><strong>placement</strong>: top, bottom, left, right (and variants)</li>
              <li><strong>overlay</strong>: React element (typically a Tooltip component)</li>
              <li><strong>children</strong>: React node or function that receives trigger handlers</li>
              <li><strong>trigger</strong>: hover, click, focus (defaults to hover)</li>
              <li><strong>delay</strong>: number or object with show/hide delays</li>
              <li><strong>show/onToggle</strong>: controlled component support</li>
            </ul>
            <Text.P2 className="mt-4 text-gray-600">
              When migrating modules from feedback-frontend, these components will work as drop-in replacements 
              without requiring any code changes.
            </Text.P2>
          </Card.Body>
        </Card>
      </div>
    </Layout.Container>
  );
};

export default TooltipDemo;
